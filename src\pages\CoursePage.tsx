import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { BookOpen, ChevronLeft, ChevronRight, Clock, Users, Star } from 'lucide-react';
import { motion } from 'motion/react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from '@/hooks/useTranslation';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import Chapter1Content from '@/components/Chapter1Content';
import Chapter2Content from '@/components/Chapter2Content';
import Chapter3Content from '@/components/Chapter3Content';
import Chapter4Content from '@/components/Chapter4Content';
import Chapter5Content from '@/components/Chapter5Content';
import Chapter6Content from '@/components/Chapter6Content';
import Chapter7Content from '@/components/Chapter7Content';
import Chapter11Content from '@/components/Chapter11Content';
import Chapter12Content from '@/components/Chapter12Content';

const CoursePage = () => {
  const { language } = useLanguage();
  const { t } = useTranslation();
  const [activeChapter, setActiveChapter] = useState('chapter1');

  const chapters = [
  {
    id: 'chapter1',
    title: 'الفصل الأول: مقدمة في التصوير بالأشعة السينية',
    titleEn: 'Chapter 1: Introduction to X-Ray Imaging',
    description: 'أساسيات التصوير الشعاعي والمبادئ التاريخية',
    descriptionEn: 'Fundamentals of radiographic imaging and historical principles',
    duration: '45 دقيقة',
    durationEn: '45 minutes',
    level: 'مبتدئ',
    levelEn: 'Beginner',
    component: Chapter1Content,
    part: 'الجزء الأول'
  },
  {
    id: 'chapter2',
    title: 'الفصل الثاني: الفيزياء الأساسية للأشعة السينية',
    titleEn: 'Chapter 2: Basic Physics of X-Rays',
    description: 'المبادئ الفيزيائية والرياضية للمحاكاة',
    descriptionEn: 'Physical and mathematical principles for simulation',
    duration: '60 دقيقة',
    durationEn: '60 minutes',
    level: 'متوسط',
    levelEn: 'Intermediate',
    component: Chapter2Content,
    part: 'الجزء الأول'
  },
  {
    id: 'chapter3',
    title: 'الفصل الثالث: أنبوب الأشعة السينية',
    titleEn: 'Chapter 3: X-Ray Tube Design',
    description: 'التصميم والمكونات والوظيفة',
    descriptionEn: 'Design, components and function',
    duration: '70 دقيقة',
    durationEn: '70 minutes',
    level: 'متوسط',
    levelEn: 'Intermediate',
    component: Chapter3Content,
    part: 'الجزء الثاني'
  },
  {
    id: 'chapter4',
    title: 'الفصل الرابع: فيزياء توليد الأشعة السينية',
    titleEn: 'Chapter 4: X-Ray Generation Physics',
    description: 'في أنابيب التشخيص من الإلكترون إلى الفوتون',
    descriptionEn: 'In diagnostic tubes from electron to photon',
    duration: '80 دقيقة',
    durationEn: '80 minutes',
    level: 'متوسط',
    levelEn: 'Intermediate',
    component: Chapter4Content,
    part: 'الجزء الثاني'
  },
  {
    id: 'chapter5',
    title: 'الفصل الخامس: مولدات الجهد العالي',
    titleEn: 'Chapter 5: High Voltage Generators',
    description: 'والتحكم في شعاع الأشعة السينية',
    descriptionEn: 'And X-ray beam control',
    duration: '65 دقيقة',
    durationEn: '65 minutes',
    level: 'متوسط',
    levelEn: 'Intermediate',
    component: Chapter5Content,
    part: 'الجزء الثاني'
  },
  {
    id: 'chapter6',
    title: 'الفصل السادس: ترشيح شعاع الأشعة السينية',
    titleEn: 'Chapter 6: X-Ray Beam Filtration',
    description: 'والجودة الشعاعية',
    descriptionEn: 'And beam quality',
    duration: '60 دقيقة',
    durationEn: '60 minutes',
    level: 'متوسط',
    levelEn: 'Intermediate',
    component: Chapter6Content,
    part: 'الجزء الثاني'
  },
  {
    id: 'chapter7',
    title: 'الفصل السابع: محاكاة أطياف الأشعة السينية',
    titleEn: 'Chapter 7: X-Ray Spectra Simulation',
    description: 'التقنيات والأدوات',
    descriptionEn: 'Techniques and tools',
    duration: '85 دقيقة',
    durationEn: '85 minutes',
    level: 'متقدم',
    levelEn: 'Advanced',
    component: Chapter7Content,
    part: 'الجزء الثاني'
  },
  {
    id: 'chapter11',
    title: 'الفصل الحادي عشر: التطبيقات المتقدمة',
    titleEn: 'Chapter 11: Advanced Applications',
    description: 'تطبيقات متقدمة في التصوير الطبي',
    descriptionEn: 'Advanced applications in medical imaging',
    duration: '75 دقيقة',
    durationEn: '75 minutes',
    level: 'متقدم',
    levelEn: 'Advanced',
    component: Chapter11Content,
    part: 'الجزء الرابع'
  },
  {
    id: 'chapter12',
    title: 'الفصل الثاني عشر: المستقبل والابتكار',
    titleEn: 'Chapter 12: Future and Innovation',
    description: 'الاتجاهات المستقبلية في التصوير',
    descriptionEn: 'Future trends in imaging technology',
    duration: '50 دقيقة',
    durationEn: '50 minutes',
    level: 'متقدم',
    levelEn: 'Advanced',
    component: Chapter12Content,
    part: 'الجزء الخامس'
  }];

  const currentChapter = chapters.find((ch) => ch.id === activeChapter);
  const currentIndex = chapters.findIndex((ch) => ch.id === activeChapter);
  const ActiveComponent = currentChapter?.component || Chapter1Content;

  const nextChapter = () => {
    const nextIndex = (currentIndex + 1) % chapters.length;
    setActiveChapter(chapters[nextIndex].id);
  };

  const prevChapter = () => {
    const prevIndex = currentIndex === 0 ? chapters.length - 1 : currentIndex - 1;
    setActiveChapter(chapters[prevIndex].id);
  };

  const isRTL = language === 'ar';

  // تجميع الفصول حسب الأجزاء
  const chaptersByPart = chapters.reduce((acc, chapter) => {
    const part = chapter.part || 'أخرى';
    if (!acc[part]) {
      acc[part] = [];
    }
    acc[part].push(chapter);
    return acc;
  }, {} as Record<string, typeof chapters>);

  const totalDuration = chapters.reduce((total, chapter) => {
    const duration = parseInt(chapter.duration.match(/\d+/)?.[0] || '0');
    return total + duration;
  }, 0);

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 ${isRTL ? 'rtl' : 'ltr'}`} data-id="9q3gtow19" data-path="src/pages/CoursePage.tsx">
      <div className="container mx-auto px-4 py-8" data-id="b1i6tsstx" data-path="src/pages/CoursePage.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex justify-between items-center mb-8" data-id="ruz31c4kq" data-path="src/pages/CoursePage.tsx">

          <div className={isRTL ? 'text-right' : 'text-left'} data-id="reup62u9e" data-path="src/pages/CoursePage.tsx">
            <h1 className="text-4xl font-bold text-gray-900 mb-2" data-id="k45y1ilmj" data-path="src/pages/CoursePage.tsx">
              {isRTL ? 'دورة التصوير بالأشعة السينية والمحاكاة' : 'X-Ray Imaging and Simulation Course'}
            </h1>
            <p className="text-gray-600" data-id="zutwdt4xp" data-path="src/pages/CoursePage.tsx">
              {isRTL ? 'دورة شاملة: من الأساسيات إلى التطبيقات المتقدمة' : 'Comprehensive Course: From Fundamentals to Advanced Applications'}
            </p>
          </div>
          <LanguageSwitcher data-id="p4fckti5q" data-path="src/pages/CoursePage.tsx" />
        </motion.div>

        {/* Course Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="grid md:grid-cols-3 gap-6 mb-8" data-id="w2diwy6zr" data-path="src/pages/CoursePage.tsx">

          <Card data-id="l4aiw378z" data-path="src/pages/CoursePage.tsx">
            <CardContent className="p-6 text-center" data-id="gvsw1sy8t" data-path="src/pages/CoursePage.tsx">
              <Clock className="w-8 h-8 mx-auto mb-2 text-blue-600" data-id="yvxzv7gcc" data-path="src/pages/CoursePage.tsx" />
              <h3 className="font-semibold mb-1" data-id="yizlejcoj" data-path="src/pages/CoursePage.tsx">
                {isRTL ? 'المدة الإجمالية' : 'Total Duration'}
              </h3>
              <p className="text-gray-600" data-id="5at453xl0" data-path="src/pages/CoursePage.tsx">{isRTL ? `${totalDuration} دقيقة` : `${totalDuration} minutes`}</p>
            </CardContent>
          </Card>
          <Card data-id="8laj34o8n" data-path="src/pages/CoursePage.tsx">
            <CardContent className="p-6 text-center" data-id="0v200o72b" data-path="src/pages/CoursePage.tsx">
              <BookOpen className="w-8 h-8 mx-auto mb-2 text-green-600" data-id="khpenpmdw" data-path="src/pages/CoursePage.tsx" />
              <h3 className="font-semibold mb-1" data-id="6da0ade2s" data-path="src/pages/CoursePage.tsx">
                {isRTL ? 'عدد الفصول' : 'Chapters'}
              </h3>
              <p className="text-gray-600" data-id="38e29ilwy" data-path="src/pages/CoursePage.tsx">{chapters.length}</p>
            </CardContent>
          </Card>
          <Card data-id="blils1d7y" data-path="src/pages/CoursePage.tsx">
            <CardContent className="p-6 text-center" data-id="mmblp2l5z" data-path="src/pages/CoursePage.tsx">
              <Star className="w-8 h-8 mx-auto mb-2 text-yellow-600" data-id="0yir1u8yx" data-path="src/pages/CoursePage.tsx" />
              <h3 className="font-semibold mb-1" data-id="5pzuf5ynf" data-path="src/pages/CoursePage.tsx">
                {isRTL ? 'المستوى' : 'Level'}
              </h3>
              <p className="text-gray-600" data-id="mnqtb4vjq" data-path="src/pages/CoursePage.tsx">{isRTL ? 'مبتدئ إلى متقدم' : 'Beginner to Advanced'}</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Main Content */}
        <div className="grid lg:grid-cols-4 gap-8" data-id="sh3u2w7y3" data-path="src/pages/CoursePage.tsx">
          {/* Chapter Navigation */}
          <motion.div
            initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="lg:col-span-1" data-id="hv4ghks6n" data-path="src/pages/CoursePage.tsx">

            <Card className="sticky top-8" data-id="0on1csz5j" data-path="src/pages/CoursePage.tsx">
              <CardHeader data-id="atel8xb54" data-path="src/pages/CoursePage.tsx">
                <CardTitle className={isRTL ? 'text-right' : 'text-left'} data-id="7mdek0yku" data-path="src/pages/CoursePage.tsx">
                  {isRTL ? 'فهرس الفصول' : 'Table of Contents'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4" data-id="lvlmmu9bj" data-path="src/pages/CoursePage.tsx">
                {Object.entries(chaptersByPart).map(([part, partChapters]) =>
                <div key={part} data-id="nn4fpra0o" data-path="src/pages/CoursePage.tsx">
                    <h4 className={`font-semibold text-sm text-primary mb-2 ${isRTL ? 'text-right' : 'text-left'}`} data-id="5vdyt8t2l" data-path="src/pages/CoursePage.tsx">
                      {part}
                    </h4>
                    <div className="space-y-2" data-id="hvb3c9pjx" data-path="src/pages/CoursePage.tsx">
                      {partChapters.map((chapter) =>
                    <Button
                      key={chapter.id}
                      variant={activeChapter === chapter.id ? 'default' : 'ghost'}
                      className={`w-full justify-start ${isRTL ? 'text-right' : 'text-left'} h-auto p-3`}
                      onClick={() => setActiveChapter(chapter.id)} data-id="b9rx3jkl1" data-path="src/pages/CoursePage.tsx">

                          <div className="flex-1" data-id="c0d92focg" data-path="src/pages/CoursePage.tsx">
                            <div className="font-medium text-sm mb-1" data-id="t5riferwa" data-path="src/pages/CoursePage.tsx">
                              {isRTL ? chapter.title : chapter.titleEn}
                            </div>
                            <div className="text-xs opacity-70" data-id="o9aby8pim" data-path="src/pages/CoursePage.tsx">
                              {isRTL ? chapter.duration : chapter.durationEn}
                            </div>
                            <Badge
                          variant="secondary"
                          className="mt-1"
                          size="sm" data-id="hn7m4pc0s" data-path="src/pages/CoursePage.tsx">

                              {isRTL ? chapter.level : chapter.levelEn}
                            </Badge>
                          </div>
                        </Button>
                    )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Chapter Content */}
          <motion.div
            key={activeChapter}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="lg:col-span-3" data-id="sky92gpz8" data-path="src/pages/CoursePage.tsx">

            <Card className="mb-6" data-id="yggduzwx9" data-path="src/pages/CoursePage.tsx">
              <CardHeader data-id="c4ovt00aj" data-path="src/pages/CoursePage.tsx">
                <div className="flex justify-between items-start" data-id="y2dlp0p5p" data-path="src/pages/CoursePage.tsx">
                  <div className={isRTL ? 'text-right' : 'text-left'} data-id="8wd1sy9wx" data-path="src/pages/CoursePage.tsx">
                    <CardTitle className="text-2xl mb-2" data-id="pm5mfrysh" data-path="src/pages/CoursePage.tsx">
                      {isRTL ? currentChapter?.title : currentChapter?.titleEn}
                    </CardTitle>
                    <CardDescription data-id="7u35vuw8q" data-path="src/pages/CoursePage.tsx">
                      {isRTL ? currentChapter?.description : currentChapter?.descriptionEn}
                    </CardDescription>
                    <div className="mt-2 flex gap-2" data-id="ifm180g3x" data-path="src/pages/CoursePage.tsx">
                      <Badge variant="outline" data-id="tmche07xu" data-path="src/pages/CoursePage.tsx">
                        {currentChapter?.part}
                      </Badge>
                      <Badge variant="secondary" data-id="egj347o1o" data-path="src/pages/CoursePage.tsx">
                        {isRTL ? currentChapter?.level : currentChapter?.levelEn}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex gap-2" data-id="ouxn5kr0w" data-path="src/pages/CoursePage.tsx">
                    <Badge variant="outline" data-id="ayaggtt2b" data-path="src/pages/CoursePage.tsx">
                      {currentIndex + 1} / {chapters.length}
                    </Badge>
                  </div>
                </div>
                <Progress value={(currentIndex + 1) / chapters.length * 100} className="mt-4" data-id="qv50ch0zt" data-path="src/pages/CoursePage.tsx" />
              </CardHeader>
            </Card>

            {/* Chapter Component */}
            <ActiveComponent data-id="0sd292pw7" data-path="src/pages/CoursePage.tsx" />

            {/* Navigation */}
            <Card className="mt-6" data-id="7kcpf1rsx" data-path="src/pages/CoursePage.tsx">
              <CardContent className="p-6" data-id="l2ndgv7wi" data-path="src/pages/CoursePage.tsx">
                <div className="flex justify-between items-center" data-id="gskthwxp9" data-path="src/pages/CoursePage.tsx">
                  <Button
                    onClick={prevChapter}
                    variant="outline"
                    className="flex items-center gap-2" data-id="hkf4r0m32" data-path="src/pages/CoursePage.tsx">

                    {isRTL ?
                    <>
                        <span data-id="2k59b95au" data-path="src/pages/CoursePage.tsx">الفصل السابق</span>
                        <ChevronRight className="w-4 h-4" data-id="4fgd61upa" data-path="src/pages/CoursePage.tsx" />
                      </> :

                    <>
                        <ChevronLeft className="w-4 h-4" data-id="gp14fvtem" data-path="src/pages/CoursePage.tsx" />
                        <span data-id="ex3xwsyei" data-path="src/pages/CoursePage.tsx">Previous Chapter</span>
                      </>
                    }
                  </Button>
                  
                  <div className="text-center" data-id="5r5nujpz3" data-path="src/pages/CoursePage.tsx">
                    <p className="text-sm text-gray-600" data-id="ebxekrtyt" data-path="src/pages/CoursePage.tsx">
                      {isRTL ? `الفصل ${currentIndex + 1} من ${chapters.length}` : `Chapter ${currentIndex + 1} of ${chapters.length}`}
                    </p>
                  </div>
                  
                  <Button
                    onClick={nextChapter}
                    className="flex items-center gap-2" data-id="99w92pi2v" data-path="src/pages/CoursePage.tsx">

                    {isRTL ?
                    <>
                        <ChevronLeft className="w-4 h-4" data-id="htkra4wkw" data-path="src/pages/CoursePage.tsx" />
                        <span data-id="wkk5j79ky" data-path="src/pages/CoursePage.tsx">الفصل التالي</span>
                      </> :

                    <>
                        <span data-id="kbgcsm4n8" data-path="src/pages/CoursePage.tsx">Next Chapter</span>
                        <ChevronRight className="w-4 h-4" data-id="zhwf1w0e0" data-path="src/pages/CoursePage.tsx" />
                      </>
                    }
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>);

};

export default CoursePage;