import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Cpu, Lightbulb, Zap, Activity, Image, Grid, PlayCircle, Calculator } from 'lucide-react';
import { motion } from 'motion/react';

const Chapter12Content = () => {
  const [activeSection, setActiveSection] = useState('introduction');
  const [simulationMode, setSimulationMode] = useState(false);
  const [simulationProgress, setSimulationProgress] = useState(0);

  const sections = [
  {
    id: 'introduction',
    title: 'مقدمة - نمذجة استجابة الكاشف',
    icon: <Cpu className="w-5 h-5" data-id="fsl83t0t2" data-path="src/components/Chapter12Content.tsx" />,
    content: {
      theory: `
          محاكاة استجابة الكاشف وتكوين الصورة تتطلب نمذجة دقيقة لجميع العمليات الفيزيائية:
          
          **المراحل الأساسية للنمذجة:**
          1. **نمذجة ترسب الطاقة**: كيف تتفاعل الأشعة السينية مع مادة الكاشف
          2. **محاكاة انتشار الضوء**: انتشار الفوتونات الضوئية في المواد الومضة
          3. **نمذجة جمع الشحنات**: تحويل الإشارة الضوئية إلى إشارة كهربائية
          4. **تحليل الأنظمة الخطية**: وصف استجابة النظام باستخدام نظرية الأنظمة الخطية
          5. **تكوين صورة الإسقاط**: محاكاة تكوين الصورة الشعاعية النهائية
          
          **أهمية المحاكاة:**
          - تحسين تصميم أجهزة الكشف الجديدة
          - فهم تأثير المعلمات المختلفة على جودة الصورة
          - تطوير تقنيات معالجة الصور المتقدمة
          - التنبؤ بأداء النظام قبل التصنيع
        `,
      vrContent: 'نموذج ثلاثي الأبعاد شامل يوضح جميع مراحل تكوين الصورة',
      keyEquations: [
      'E_deposited = ∫ μ(E,x) × Φ(E,x) dE dx',
      'Light_output = η × E_deposited',
      'Signal = ∫ QE(λ) × Light(λ) dλ']

    }
  },
  {
    id: 'energy-deposition',
    title: '12.1 نمذجة ترسب الطاقة',
    icon: <Zap className="w-5 h-5" data-id="v30zm3pcc" data-path="src/components/Chapter12Content.tsx" />,
    content: {
      theory: `
          **نمذجة ترسب الطاقة في مواد الكاشف:**
          
          ترسب الطاقة هو الخطوة الأولى في عملية الكشف، حيث تتفاعل الأشعة السينية مع ذرات المادة.
          
          **التفاعلات الأساسية:**
          
          **1. التأثير الكهروضوئي (Photoelectric Effect):**
          - يحدث عند الطاقات المنخفضة (<100 keV)
          - الفوتون يُمتص كلياً ويطلق إلكترون ضوئي
          - طاقة الإلكترون: E_e = E_photon - B_k
          
          **2. تشتت كومبتون (Compton Scattering):**
          - يحدث عند الطاقات المتوسطة (100-1000 keV)
          - الفوتون يفقد جزء من طاقته للإلكترون
          - طاقة الفوتون المتشتت: E' = E / (1 + E/m_e c² (1-cos θ))
          
          **3. إنتاج الأزواج (Pair Production):**
          - يحدث عند الطاقات العالية (>1.022 MeV)
          - الفوتون يتحول إلى زوج إلكترون-بوزيترون
          
          **نمذجة Monte Carlo:**
          
          محاكاة Monte Carlo تتبع كل فوتون فردياً
          
          **معادلات أساسية:**
          
          معامل التوهين الكلي:
          μ_total = μ_photoelectric + μ_compton + μ_pair
          
          احتمالية التفاعل:
          P(x) = 1 - exp(-μ × x)
          
          توزيع ترسب الطاقة:
          dE/dx = ρ × Σ σ_i × N_i × E_i
        `,
      vrContent: 'محاكاة ثلاثية الأبعاد لمسارات الفوتونات وترسب الطاقة في الكاشف',
      materialProperties: {
        'CsI': { density: '4.51 g/cm³', Z_eff: '54', mu_photoelectric: 'عالي عند 60 keV' },
        'a-Se': { density: '4.28 g/cm³', Z_eff: '34', mu_photoelectric: 'متوسط عند 60 keV' },
        'Gd2O2S': { density: '7.32 g/cm³', Z_eff: '61', mu_photoelectric: 'عالي جداً عند 60 keV' }
      }
    }
  },
  {
    id: 'light-transport',
    title: '12.2 محاكاة انتشار ضوء الومض',
    icon: <Lightbulb className="w-5 h-5" data-id="mhz1ef3le" data-path="src/components/Chapter12Content.tsx" />,
    content: {
      theory: `
          **محاكاة انتشار ضوء الومض والتداخل البصري:**
          
          بعد ترسب الطاقة، تحتاج المواد الومضة إلى تحويل هذه الطاقة إلى ضوء مرئي.
          
          **عملية الومض (Scintillation Process):**
          
          **1. الإثارة الأولية:**
          - الإلكترونات المثارة بالأشعة السينية تنقل طاقتها للشبكة البلورية
          - تكوين أزواج إلكترون-ثقب أو إثارة مراكز الإضاءة
          
          **2. الإصدار الضوئي:**
          - الإلكترونات المثارة تعود لحالتها الأساسية مصدرة فوتونات ضوئية
          - طيف الإصدار يعتمد على نوع المادة الومضة
          
          **3. انتشار الضوء:**
          الضوء المنبعث ينتشر في المادة ويخضع لعدة عمليات:
          
          **الامتصاص الذاتي (Self-absorption):**
          I(x) = I_0 × exp(-α × x)
          
          حيث α معامل الامتصاص للمادة الومضة.
          
          **التشتت (Scattering):**
          - تشتت رايلي: يعتمد على λ^-4
          - تشتت مي: يحدث مع الجسيمات الكبيرة
          
          **الانعكاس والانكسار:**
          - عند الحدود بين المواد المختلفة
          - قانون سنيل: n₁ sin θ₁ = n₂ sin θ₂
          
          **التداخل البصري (Optical Crosstalk):**
          
          الضوء المنتشر يمكن أن يصل إلى بكسل مجاور، مما يسبب:
          - تدهور الدقة المكانية
          - انخفاض التباين
          - تشويش في الصورة
          
          معامل التداخل البصري:
          OCF = (Signal_adjacent)/(Signal_central)
          
          **تحسين التصميم:**
          - استخدام عاكسات بصرية بين البكسل
          - تحسين سماكة طبقة الومض
          - اختيار مواد ذات معامل انكسار مناسب
        `,
      vrContent: 'تصور ثلاثي الأبعاد لانتشار الضوء داخل المادة الومضة مع إظهار التداخل البصري',
      scintillatorProperties: {
        'CsI:Tl': {
          lightYield: '54,000 photons/MeV',
          peakWavelength: '550 nm',
          decayTime: '1 μs',
          refractiveIndex: '1.79'
        },
        'Gd2O2S:Tb': {
          lightYield: '60,000 photons/MeV',
          peakWavelength: '545 nm',
          decayTime: '1 ms',
          refractiveIndex: '1.95'
        }
      }
    }
  },
  {
    id: 'charge-collection',
    title: '12.3 نمذجة جمع الشحنات',
    icon: <Activity className="w-5 h-5" data-id="2onvcxhw0" data-path="src/components/Chapter12Content.tsx" />,
    content: {
      theory: `
          **نمذجة جمع الشحنات والضوضاء الإلكترونية:**
          
          المرحلة الأخيرة في عملية الكشف هي تحويل الإشارة الضوئية إلى إشارة كهربائية.
          
          **عملية جمع الشحنات:**
          
          **1. التحويل الضوئي-الكهربائي:**
          في أجهزة الكشف الرقمية، يتم تحويل الضوء إلى شحنات كهربائية:
          
          **للكاشفات غير المباشرة:**
          - الثنائيات الضوئية تحول الفوتونات إلى أزواج إلكترون-ثقب
          - كفاءة التحويل: QE(λ) = η_quantum × (1 - R(λ)) × (1 - exp(-α(λ)d))
          
          **للكاشفات المباشرة:**
          - الأشعة السينية تولد الشحنات مباشرة
          - عدد أزواج الشحنة: N = E_photon / W_pair
          
          **2. انجراف الشحنات:**
          الشحنات المتولدة تنجرف تحت تأثير المجال الكهربائي:
          
          سرعة الانجراف: v_drift = μ × E_field
          
          حيث μ هي حركية الحاملات (electron/hole mobility).
          
          **3. جمع الشحنات في البكسل:**
          الشحنات تُجمع في مكثفات البكسل:
          
          الجهد المتكون: V = Q / C_pixel
          
          **مصادر الضوضاء الإلكترونية:**
          
          **1. الضوضاء الكمية (Shot Noise):**
          σ_shot² = N_electrons
          
          **2. ضوضاء الحرارة (Thermal Noise):**
          σ_thermal² = 4kT × BW / R
          
          **3. ضوضاء التيار المظلم (Dark Current Noise):**
          σ_dark² = I_dark × t_integration
          
          **4. ضوضاء المضخم (Amplifier Noise):**
          σ_amp² = (V_noise / Gain)²
          
          **إجمالي الضوضاء:**
          σ_total² = σ_shot² + σ_thermal² + σ_dark² + σ_amp²
          
          **عوامل تؤثر على جمع الشحنات:**
          - سماكة طبقة الكشف
          - قوة المجال الكهربائي
          - درجة الحرارة
          - عيوب في البلورة أو الشوائب
          - زمن التكامل (Integration Time)
          
          **تحسين عملية جمع الشحنات:**
          - زيادة الجهد المطبق (للكاشفات المباشرة)
          - تقليل درجة الحرارة
          - استخدام مواد عالية النقاء
          - تحسين تصميم الدوائر الإلكترونية
        `,
      vrContent: 'نموذج تفاعلي يوضح انجراف الشحنات وعملية جمعها في البكسل مع تأثيرات الضوضاء',
      noiseAnalysis: {
        'Shot Noise': 'متناسب مع جذر عدد الفوتونات',
        'Thermal Noise': 'متناسب مع جذر درجة الحرارة والتردد',
        'Dark Current': 'يزداد مع الوقت ودرجة الحرارة',
        'Read Noise': 'ثابت لكل قراءة'
      }
    }
  },
  {
    id: 'linear-systems',
    title: '12.4 تحليل الأنظمة الخطية',
    icon: <Calculator className="w-5 h-5" data-id="dp52tnrqx" data-path="src/components/Chapter12Content.tsx" />,
    content: {
      theory: `
          **تحليل الأنظمة الخطية المتتالية لنمذجة الكاشف:**
          
          نظرية الأنظمة الخطية توفر إطار عمل رياضي لتحليل أداء أجهزة الكشف.
          
          **مبادئ النظام الخطي:**
          
          **1. الخطية (Linearity):**
          إذا كان f(x) يعطي y(x)، فإن a×f(x) يعطي a×y(x)
          
          **2. عدم التغير المكاني (Shift Invariance):**
          إذا كان f(x) يعطي y(x)، فإن f(x-a) يعطي y(x-a)
          
          **وصف النظام بالدوال:**
          
          **دالة الاستجابة النقطية (Point Spread Function - PSF):**
          تصف استجابة النظام لنقطة ضوء واحدة.
          
          **دالة الاستجابة الخطية (Line Spread Function - LSF):**
          تصف استجابة النظام لخط رفيع من الضوء.
          
          العلاقة: LSF(x) = ∫ PSF(x,y) dy
          
          **دالة نقل التعديل (Modulation Transfer Function - MTF):**
          MTF(f) = |FT[LSF(x)]| / |FT[LSF(x)]|_{f=0}
          
          **النمذجة المتتالية (Cascade Model):**
          
          كاشف الأشعة السينية يمكن نمذجته كسلسلة من المراحل:
          
          **المرحلة 1: امتصاص الأشعة السينية**
          - عدد الفوتونات الممتصة: N₁ = N₀ × η_abs
          - MTF₁(f) = sinc(πfd₁) حيث d₁ سماكة طبقة الامتصاص
          
          **المرحلة 2: تحويل الأشعة إلى ضوء**
          - عدد الفوتونات الضوئية: N₂ = N₁ × g حيث g معامل التكثيف
          - MTF₂(f) يعتمد على انتشار الضوء
          
          **المرحلة 3: كشف الضوء**
          - عدد الإلكترونات: N₃ = N₂ × QE
          - MTF₃(f) يعتمد على حجم البكسل
          
          **الاستجابة الإجمالية:**
          MTF_total(f) = MTF₁(f) × MTF₂(f) × MTF₃(f)
          
          **طيف قدرة الضوضاء (Noise Power Spectrum - NPS):**
          
          NPS يصف التوزيع التردي للضوضاء:
          NPS(f) = |FT[noise_autocorrelation(x)]|
          
          **للضوضاء الكمية:**
          NPS_quantum(f) = constant (أبيض)
          
          **للضوضاء المترابطة:**
          NPS_correlated(f) = NPS_quantum(f) × |MTF_noise(f)|²
          
          **كفاءة الكم الاستقصائية (Detective Quantum Efficiency - DQE):**
          
          DQE تجمع بين MTF وNPS:
          DQE(f) = [MTF(f)]² × QE / [NPS(f) / (q₀ × Δx²)]
          
          حيث:
          - q₀: عدد الفوتونات الساقطة لكل وحدة مساحة
          - Δx: حجم البكسل
          
          **تطبيقات التحليل:**
          - تحسين تصميم الكاشف
          - مقارنة أداء أنواع مختلفة من الكاشفات
          - التنبؤ بجودة الصورة
          - تصميم فلاتر معالجة الصور المثلى
        `,
      vrContent: 'تصور تفاعلي للدوال MTF وNPS وDQE مع إمكانية تغيير معلمات النظام',
      cascadeStages: {
        'X-ray absorption': { gain: 'η_abs', mtf: 'sinc function', noise: 'quantum' },
        'Light conversion': { gain: 'light_yield', mtf: 'spread function', noise: 'conversion' },
        'Light detection': { gain: 'QE', mtf: 'pixel aperture', noise: 'electronic' }
      }
    }
  },
  {
    id: 'projection-imaging',
    title: '12.5 محاكاة تكوين صورة الإسقاط',
    icon: <Image className="w-5 h-5" data-id="wabmbha09" data-path="src/components/Chapter12Content.tsx" />,
    content: {
      theory: `
          **محاكاة تكوين صورة الإسقاط الشعاعي:**
          
          تكوين صورة الإسقاط هو العملية النهائية في التصوير الشعاعي، حيث تتحول الأشعة المخترقة للمريض إلى صورة مرئية.
          
          **مبادئ الإسقاط الشعاعي:**
          
          **قانون بير-لامبرت (Beer-Lambert Law):**
          الأساس الرياضي لتوهين الأشعة السينية:
          
          I(x,y) = I₀ × exp(-∫ μ(E,z) dz)
          
          حيث:
          - I₀: شدة الشعاع الساقط
          - μ(E,z): معامل التوهين كدالة للطاقة والموقع
          - z: عمق النفاذ في الجسم
          
          **الهندسة الإسقاطية:**
          
          **تكبير هندسي:**
          M = (SID) / (SOD)
          
          حيث:
          - SID: المسافة من المصدر إلى الكاشف
          - SOD: المسافة من المصدر إلى الجسم
          
          **عدم الوضوح الهندسي:**
          U_g = f × (SID - SOD) / SOD
          
          حيث f حجم البؤرة الفعالة.
          
          **نمذجة المريض:**
          
          **الأشباح الرقمية (Digital Phantoms):**
          تمثيل رقمي لتشريح الإنسان
          
          **تأثيرات التشويه:**
          
          **1. عدم الوضوح الحركي:**
          بسبب حركة المريض أثناء التعرض:
          MTF_motion(f) = sinc(π × f × v × t_exposure)
          
          **2. عدم الوضوح بسبب حجم البؤرة:**
          PSF_focal(x,y) = rect(x/f_x) × rect(y/f_y)
          
          **3. التشتت:**
          الأشعة المتشتتة تقلل تباين الصورة:
          SPR = (Scattered Radiation) / (Primary Radiation)
          
          **معايير جودة الصورة:**
          
          **نسبة الإشارة إلى الضوضاء (SNR):**
          SNR = (Signal_mean) / (Noise_std)
          
          **نسبة التباين إلى الضوضاء (CNR):**
          CNR = |Signal_object - Signal_background| / Noise_std
          
          **دقة التباين المنخفض:**
          LCR = ΔI / I_background
          
          **تطبيقات المحاكاة:**
          - تحسين بروتوكولات التصوير
          - تطوير خوارزميات إعادة البناء
          - تقييم جرعة المريض
          - تدريب الأطباء وتقنيي الأشعة
          - تطوير أنظمة الذكاء الاصطناعي
        `,
      vrContent: 'محاكاة كاملة لعملية التصوير مع إمكانية تغيير معلمات الإعداد ومشاهدة تأثيرها على الصورة',
      imagingParameters: {
        'kVp': { range: '80-140 kV', effect: 'يؤثر على نفاذية الأشعة وتباين الصورة' },
        'mAs': { range: '1-1000 mAs', effect: 'يحدد كمية الأشعة وضوضاء الصورة' },
        'Filtration': { types: 'Al, Cu, Mo', effect: 'يحسن جودة الطيف ويقلل الجرعة' },
        'Grid ratio': { range: '8:1 - 16:1', effect: 'يقلل التشتت ويحسن التباين' }
      }
    }
  }];


  const SimulationVisualization = ({ section }: {section: any;}) => {
    const startSimulation = () => {
      setSimulationMode(true);
      setSimulationProgress(0);

      const interval = setInterval(() => {
        setSimulationProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            setTimeout(() => setSimulationMode(false), 2000);
            return 100;
          }
          return prev + 5;
        });
      }, 200);
    };

    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-gradient-to-br from-slate-800 to-slate-900 p-6 rounded-lg text-white" data-id="vn2wi7z6q" data-path="src/components/Chapter12Content.tsx">

        <div className="flex items-center justify-between mb-4" data-id="wk1c6dyw1" data-path="src/components/Chapter12Content.tsx">
          <h3 className="text-lg font-semibold text-right" data-id="skcs4wsro" data-path="src/components/Chapter12Content.tsx">محاكاة تفاعلية</h3>
          <Button
            variant="secondary"
            size="sm"
            onClick={startSimulation}
            disabled={simulationMode} data-id="642hac2e0" data-path="src/components/Chapter12Content.tsx">

            <PlayCircle className="w-4 h-4 ml-2" data-id="az5ryboyk" data-path="src/components/Chapter12Content.tsx" />
            {simulationMode ? 'جاري المحاكاة...' : 'بدء المحاكاة'}
          </Button>
        </div>
        
        <div className="aspect-video bg-slate-700 rounded-lg flex items-center justify-center mb-4" data-id="g5dqfuntx" data-path="src/components/Chapter12Content.tsx">
          {simulationMode ?
          <motion.div className="text-center" data-id="qmrlyhung" data-path="src/components/Chapter12Content.tsx">
              <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4" data-id="6701qtx5x" data-path="src/components/Chapter12Content.tsx" />

              <p className="text-lg mb-2" data-id="hi062wynm" data-path="src/components/Chapter12Content.tsx">{section.content.vrContent}</p>
              <Progress value={simulationProgress} className="max-w-sm mx-auto" data-id="jzhr9zm1a" data-path="src/components/Chapter12Content.tsx" />
              <p className="text-sm mt-2" data-id="46hnka906" data-path="src/components/Chapter12Content.tsx">{simulationProgress}% مكتمل</p>
            </motion.div> :

          <div className="text-center" data-id="jseoyz4ac" data-path="src/components/Chapter12Content.tsx">
              <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4" data-id="4wpvut2uh" data-path="src/components/Chapter12Content.tsx">
                {section.icon}
              </div>
              <p data-id="l4mivef0w" data-path="src/components/Chapter12Content.tsx">انقر على "بدء المحاكاة" لرؤية العرض التفاعلي</p>
            </div>
          }
        </div>
        
        {section.content.keyEquations &&
        <div className="bg-slate-600 p-4 rounded-lg" data-id="sn61sc52z" data-path="src/components/Chapter12Content.tsx">
            <h4 className="font-semibold mb-2 text-right" data-id="u5nd8elhb" data-path="src/components/Chapter12Content.tsx">المعادلات الرئيسية:</h4>
            {section.content.keyEquations.map((eq: string, index: number) =>
          <div key={index} className="text-sm font-mono bg-slate-700 p-2 rounded mb-2 text-center" data-id="ofu1terdi" data-path="src/components/Chapter12Content.tsx">
                {eq}
              </div>
          )}
          </div>
        }
      </motion.div>);

  };

  const currentSection = sections.find((s) => s.id === activeSection);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6" dir="rtl" data-id="hfwk8ciwz" data-path="src/components/Chapter12Content.tsx">
      <div className="max-w-7xl mx-auto" data-id="782txw0oi" data-path="src/components/Chapter12Content.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8" data-id="v0h13g8wh" data-path="src/components/Chapter12Content.tsx">

          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4" data-id="4ei09ssyo" data-path="src/components/Chapter12Content.tsx">
            الفصل الثاني عشر: محاكاة استجابة الكاشف وتكوين الصورة
          </h1>
          <p className="text-xl text-muted-foreground" data-id="i3pagqdyl" data-path="src/components/Chapter12Content.tsx">
            نمذجة متقدمة وتحليل شامل لعمليات تكوين الصورة الشعاعية
          </p>
          <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mt-4" data-id="fn73tvcxy" data-path="src/components/Chapter12Content.tsx">
            <Badge variant="outline" data-id="aaw5h3z96" data-path="src/components/Chapter12Content.tsx">محاكاة Monte Carlo</Badge>
            <Badge variant="outline" data-id="4w3gt3zyy" data-path="src/components/Chapter12Content.tsx">تحليل الأنظمة الخطية</Badge>
            <Badge variant="outline" data-id="0d0vbild0" data-path="src/components/Chapter12Content.tsx">معالجة الصور</Badge>
          </div>
        </motion.div>

        {/* Navigation */}
        <Card className="mb-8" data-id="rqgpv2ejj" data-path="src/components/Chapter12Content.tsx">
          <CardHeader data-id="axxt51q9o" data-path="src/components/Chapter12Content.tsx">
            <CardTitle className="text-right" data-id="f0g65zytr" data-path="src/components/Chapter12Content.tsx">محتويات الفصل</CardTitle>
          </CardHeader>
          <CardContent data-id="k6p25wmoz" data-path="src/components/Chapter12Content.tsx">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2" data-id="ug6orvbm5" data-path="src/components/Chapter12Content.tsx">
              {sections.map((section) =>
              <Button
                key={section.id}
                variant={activeSection === section.id ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveSection(section.id)}
                className="justify-end text-xs p-2 h-auto" data-id="h3h9p7ylt" data-path="src/components/Chapter12Content.tsx">

                  <span className="mr-2 text-right leading-tight" data-id="p23hfoc4m" data-path="src/components/Chapter12Content.tsx">{section.title}</span>
                  {section.icon}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-8" data-id="ot7ail2ti" data-path="src/components/Chapter12Content.tsx">
          {/* Theory Content */}
          <motion.div
            key={activeSection}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }} data-id="amjou59aw" data-path="src/components/Chapter12Content.tsx">

            <Card className="h-full" data-id="jgsugwy5w" data-path="src/components/Chapter12Content.tsx">
              <CardHeader data-id="wijafoqxx" data-path="src/components/Chapter12Content.tsx">
                <CardTitle className="text-right flex items-center space-x-3 rtl:space-x-reverse" data-id="eay7xaky6" data-path="src/components/Chapter12Content.tsx">
                  {currentSection?.icon}
                  <span data-id="zg6s4sww3" data-path="src/components/Chapter12Content.tsx">{currentSection?.title}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 max-h-[600px] overflow-y-auto" data-id="t2yi73r5a" data-path="src/components/Chapter12Content.tsx">
                <div className="prose prose-sm max-w-none text-right" style={{ direction: 'rtl' }} data-id="rlx9n120y" data-path="src/components/Chapter12Content.tsx">
                  <div className="whitespace-pre-line text-sm leading-relaxed" data-id="xi2cinupt" data-path="src/components/Chapter12Content.tsx">
                    {currentSection?.content.theory}
                  </div>
                </div>

                {/* Technical Data Tables */}
                {currentSection?.content.materialProperties &&
                <div className="bg-blue-50 p-4 rounded-lg" data-id="p2fc7y260" data-path="src/components/Chapter12Content.tsx">
                    <h4 className="font-semibold text-blue-800 text-right mb-3" data-id="g1xa062ka" data-path="src/components/Chapter12Content.tsx">خصائص المواد:</h4>
                    <div className="overflow-x-auto" data-id="e1oni22lm" data-path="src/components/Chapter12Content.tsx">
                      <table className="w-full text-sm" data-id="txnzeqev4" data-path="src/components/Chapter12Content.tsx">
                        <thead data-id="x8rclxbn9" data-path="src/components/Chapter12Content.tsx">
                          <tr className="border-b border-blue-200" data-id="47yqymzty" data-path="src/components/Chapter12Content.tsx">
                            <th className="text-right p-2" data-id="xh9slp0uf" data-path="src/components/Chapter12Content.tsx">معامل التوهين</th>
                            <th className="text-right p-2" data-id="6ajqbo9zs" data-path="src/components/Chapter12Content.tsx">العدد الذري الفعال</th>
                            <th className="text-right p-2" data-id="9lt6sbg8k" data-path="src/components/Chapter12Content.tsx">الكثافة</th>
                            <th className="text-right p-2" data-id="m7n0uihxi" data-path="src/components/Chapter12Content.tsx">المادة</th>
                          </tr>
                        </thead>
                        <tbody data-id="ea52hbp73" data-path="src/components/Chapter12Content.tsx">
                          {Object.entries(currentSection.content.materialProperties).map(([material, props]: [string, any]) =>
                        <tr key={material} className="border-b border-blue-100" data-id="1lj3hf3qq" data-path="src/components/Chapter12Content.tsx">
                              <td className="text-right p-2" data-id="6k5qg8muf" data-path="src/components/Chapter12Content.tsx">{props.mu_photoelectric}</td>
                              <td className="text-right p-2" data-id="nby9hbvul" data-path="src/components/Chapter12Content.tsx">{props.Z_eff}</td>
                              <td className="text-right p-2" data-id="2x7h3y2tj" data-path="src/components/Chapter12Content.tsx">{props.density}</td>
                              <td className="text-right p-2 font-medium" data-id="ag253cuio" data-path="src/components/Chapter12Content.tsx">{material}</td>
                            </tr>
                        )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                }

                {currentSection?.content.cascadeStages &&
                <div className="bg-green-50 p-4 rounded-lg" data-id="cu2w8ntju" data-path="src/components/Chapter12Content.tsx">
                    <h4 className="font-semibold text-green-800 text-right mb-3" data-id="dekguflx2" data-path="src/components/Chapter12Content.tsx">مراحل النموذج المتتالي:</h4>
                    <div className="space-y-3" data-id="ilsf5f4g0" data-path="src/components/Chapter12Content.tsx">
                      {Object.entries(currentSection.content.cascadeStages).map(([stage, props]: [string, any]) =>
                    <div key={stage} className="bg-white p-3 rounded border" data-id="6usfiuook" data-path="src/components/Chapter12Content.tsx">
                          <h5 className="font-medium text-right mb-2" data-id="po5iv2t5n" data-path="src/components/Chapter12Content.tsx">{stage}</h5>
                          <div className="grid grid-cols-3 gap-2 text-xs" data-id="mqn49hd18" data-path="src/components/Chapter12Content.tsx">
                            <div data-id="91hzgkjty" data-path="src/components/Chapter12Content.tsx"><span className="font-medium" data-id="isdiyf1l5" data-path="src/components/Chapter12Content.tsx">المكسب:</span> {props.gain}</div>
                            <div data-id="sa214r1oi" data-path="src/components/Chapter12Content.tsx"><span className="font-medium" data-id="rkc48eaaj" data-path="src/components/Chapter12Content.tsx">MTF:</span> {props.mtf}</div>
                            <div data-id="qwt29t1vw" data-path="src/components/Chapter12Content.tsx"><span className="font-medium" data-id="01u06cabw" data-path="src/components/Chapter12Content.tsx">الضوضاء:</span> {props.noise}</div>
                          </div>
                        </div>
                    )}
                    </div>
                  </div>
                }
              </CardContent>
            </Card>
          </motion.div>

          {/* Simulation Visualization */}
          <motion.div
            key={`${activeSection}-sim`}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }} data-id="9qgwhisiw" data-path="src/components/Chapter12Content.tsx">

            {currentSection && <SimulationVisualization section={currentSection} data-id="zrsbxvfv6" data-path="src/components/Chapter12Content.tsx" />}
          </motion.div>
        </div>

        {/* Learning Objectives and Summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8 space-y-6" data-id="9ziewlajd" data-path="src/components/Chapter12Content.tsx">

          <Card data-id="pj2kucs5q" data-path="src/components/Chapter12Content.tsx">
            <CardHeader data-id="qicogq8yy" data-path="src/components/Chapter12Content.tsx">
              <CardTitle className="text-right" data-id="qeclajilk" data-path="src/components/Chapter12Content.tsx">الأهداف التعليمية والمخرجات</CardTitle>
            </CardHeader>
            <CardContent data-id="renss9j8y" data-path="src/components/Chapter12Content.tsx">
              <div className="grid md:grid-cols-3 gap-6" data-id="37xrk1xv1" data-path="src/components/Chapter12Content.tsx">
                <div data-id="2qzr999qy" data-path="src/components/Chapter12Content.tsx">
                  <h4 className="font-semibold text-right mb-3 text-blue-600" data-id="y0twk6we0" data-path="src/components/Chapter12Content.tsx">المعرفة النظرية:</h4>
                  <ul className="text-sm space-y-1 text-right" data-id="ejnejdi1r" data-path="src/components/Chapter12Content.tsx">
                    <li data-id="znig8ajsl" data-path="src/components/Chapter12Content.tsx">• فهم الأسس الفيزيائية لعمليات الكشف</li>
                    <li data-id="rvpwu65fx" data-path="src/components/Chapter12Content.tsx">• تحليل انتشار الضوء في المواد الومضة</li>
                    <li data-id="de4k0ly7i" data-path="src/components/Chapter12Content.tsx">• نمذجة عمليات جمع الشحنات</li>
                    <li data-id="8ztzhhrc1" data-path="src/components/Chapter12Content.tsx">• تطبيق نظرية الأنظمة الخطية</li>
                  </ul>
                </div>
                <div data-id="wswb9bmaw" data-path="src/components/Chapter12Content.tsx">
                  <h4 className="font-semibold text-right mb-3 text-green-600" data-id="042eifm3a" data-path="src/components/Chapter12Content.tsx">المهارات التطبيقية:</h4>
                  <ul className="text-sm space-y-1 text-right" data-id="nkmniyfub" data-path="src/components/Chapter12Content.tsx">
                    <li data-id="9i10bwurg" data-path="src/components/Chapter12Content.tsx">• تصميم نماذج محاكاة Monte Carlo</li>
                    <li data-id="yectcpolv" data-path="src/components/Chapter12Content.tsx">• حساب MTF وNPS وDQE</li>
                    <li data-id="28r4l13bi" data-path="src/components/Chapter12Content.tsx">• تحليل تأثير المعلمات على الأداء</li>
                    <li data-id="pchyvyoob" data-path="src/components/Chapter12Content.tsx">• تطوير خوارزميات معالجة الصور</li>
                  </ul>
                </div>
                <div data-id="5vf2cm8ze" data-path="src/components/Chapter12Content.tsx">
                  <h4 className="font-semibold text-right mb-3 text-purple-600" data-id="l15ar06fo" data-path="src/components/Chapter12Content.tsx">التطبيقات العملية:</h4>
                  <ul className="text-sm space-y-1 text-right" data-id="e9ryyztsi" data-path="src/components/Chapter12Content.tsx">
                    <li data-id="a3ofqqky2" data-path="src/components/Chapter12Content.tsx">• تحسين تصميم أجهزة الكشف</li>
                    <li data-id="gxbb4f6ka" data-path="src/components/Chapter12Content.tsx">• تطوير بروتوكولات التصوير</li>
                    <li data-id="dg4a4io2e" data-path="src/components/Chapter12Content.tsx">• تقييم جودة الصورة</li>
                    <li data-id="xfmucesrs" data-path="src/components/Chapter12Content.tsx">• حل مشاكل الأداء السريرية</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>);

};

export default Chapter12Content;