import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Atom, Waves, Calculator, Target, Zap, Ruler } from 'lucide-react';
import { motion } from 'motion/react';

const Chapter2Content = () => {
  const [openSections, setOpenSections] = useState<{[key: string]: boolean;}>({});
  const [completedSections, setCompletedSections] = useState<string[]>([]);

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const markAsCompleted = (sectionId: string) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections((prev) => [...prev, sectionId]);
    }
  };

  const sections = [
  {
    id: '2.1',
    title: 'طبيعة الأشعة السينية: الإشعاع الكهرومغناطيسي',
    icon: <Waves className="w-5 h-5" data-id="4pn7vdeko" data-path="src/components/Chapter2Content.tsx" />,
    subsections: [
    {
      id: '2.1.1',
      title: 'ازدواجية الموجة والجسيم',
      content: 'الأشعة السينية تظهر خصائص موجية وجسيمية. كموجات، لها تردد وطول موجي، وكجسيمات (فوتونات)، لها طاقة وزخم محددين.'
    },
    {
      id: '2.1.2',
      title: 'علاقات الطاقة والطول الموجي والتردد',
      content: 'العلاقة الأساسية: E = hf = hc/λ، حيث E الطاقة، h ثابت بلانك، f التردد، c سرعة الضوء، λ الطول الموجي.'
    }]

  },
  {
    id: '2.2',
    title: 'الكميات والوحدات في التصوير الشعاعي',
    icon: <Ruler className="w-5 h-5" data-id="ui7tflwk4" data-path="src/components/Chapter2Content.tsx" />,
    subsections: [
    {
      id: '2.2.1',
      title: 'التعرض والكيرما والجرعة الممتصة',
      content: 'التعرض يقيس الشحنة المتأينة، الكيرما الطاقة المنقولة للمادة، والجرعة الممتصة الطاقة الممتصة لكل وحدة كتلة.'
    },
    {
      id: '2.2.2',
      title: 'الانسياب وانسياب الطاقة والشدة',
      content: 'الانسياب هو عدد الجسيمات لكل وحدة مساحة، انسياب الطاقة هو الطاقة المنقولة لكل وحدة مساحة، والشدة هي القدرة لكل وحدة مساحة.'
    }]

  },
  {
    id: '2.3',
    title: 'التفاعلات الأساسية للإشعاع مع المادة',
    icon: <Atom className="w-5 h-5" data-id="60auxtxh0" data-path="src/components/Chapter2Content.tsx" />,
    content: 'التفاعلات الرئيسية تشمل: التأثير الكهروضوئي، تشتت كومبتون، تشتت رايلي، وإنتاج الأزواج (في الطاقات العالية).'
  },
  {
    id: '2.4',
    title: 'المقدمات الرياضية للمحاكاة',
    icon: <Calculator className="w-5 h-5" data-id="4pwipuke6" data-path="src/components/Chapter2Content.tsx" />,
    subsections: [
    {
      id: '2.4.1',
      title: 'توزيعات الاحتمالات والعينات',
      content: 'فهم التوزيعات الإحصائية المختلفة وطرق أخذ العينات العشوائية ضروري لمحاكاة مونت كارلو.'
    },
    {
      id: '2.4.2',
      title: 'أنظمة الإحداثيات والتحويلات',
      content: 'أنظمة الإحداثيات المختلفة (ديكارتية، كروية، أسطوانية) والتحويلات بينها مهمة في النمذجة الهندسية.'
    }]

  }];


  const progress = completedSections.length / sections.length * 100;

  const physicsFormulas = [
  { name: 'طاقة الفوتون', formula: 'E = hf = hc/λ', description: 'العلاقة بين الطاقة والتردد والطول الموجي' },
  { name: 'قانون الامتصاص', formula: 'I = I₀e^(-μx)', description: 'قانون بير-لامبرت للامتصاص' },
  { name: 'الجرعة الممتصة', formula: 'D = dE/dm', description: 'الطاقة الممتصة لكل وحدة كتلة' },
  { name: 'الكيرما', formula: 'K = dE_tr/dm', description: 'الطاقة الحركية المنقولة لكل وحدة كتلة' }];


  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6" dir="rtl" data-id="o4wqmv15r" data-path="src/components/Chapter2Content.tsx">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }} data-id="n7oerrka4" data-path="src/components/Chapter2Content.tsx">

        <Card className="mb-6" data-id="lxuso2psk" data-path="src/components/Chapter2Content.tsx">
          <CardHeader className="text-center" data-id="35wp51n7g" data-path="src/components/Chapter2Content.tsx">
            <CardTitle className="text-2xl font-bold text-right" data-id="4aa3jok1i" data-path="src/components/Chapter2Content.tsx">
              الفصل الثاني: الفيزياء الأساسية للأشعة السينية للمحاكاة
            </CardTitle>
            <CardDescription className="text-right" data-id="eax1gxiaw" data-path="src/components/Chapter2Content.tsx">
              تعلم المبادئ الفيزيائية والرياضية الأساسية لمحاكاة الأشعة السينية
            </CardDescription>
            <div className="mt-4" data-id="qmjfh9xez" data-path="src/components/Chapter2Content.tsx">
              <div className="flex justify-between items-center mb-2" data-id="djksg7f6l" data-path="src/components/Chapter2Content.tsx">
                <span className="text-sm text-muted-foreground" data-id="xwy24jf4n" data-path="src/components/Chapter2Content.tsx">التقدم</span>
                <span className="text-sm font-medium" data-id="g5rwdhp59" data-path="src/components/Chapter2Content.tsx">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" data-id="mwn386zdh" data-path="src/components/Chapter2Content.tsx" />
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      <Tabs defaultValue="content" className="w-full" data-id="b1lu476uh" data-path="src/components/Chapter2Content.tsx">
        <TabsList className="grid w-full grid-cols-2" data-id="gnadptggg" data-path="src/components/Chapter2Content.tsx">
          <TabsTrigger value="content" data-id="cj7kf8f5n" data-path="src/components/Chapter2Content.tsx">المحتوى</TabsTrigger>
          <TabsTrigger value="formulas" data-id="cxyje9fts" data-path="src/components/Chapter2Content.tsx">المعادلات الفيزيائية</TabsTrigger>
        </TabsList>
        
        <TabsContent value="content" className="space-y-4" data-id="vfddbtf19" data-path="src/components/Chapter2Content.tsx">
          {sections.map((section, index) =>
          <motion.div
            key={section.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }} data-id="ofayqocdh" data-path="src/components/Chapter2Content.tsx">

              <Card className="overflow-hidden" data-id="hv9da68xa" data-path="src/components/Chapter2Content.tsx">
                <Collapsible
                open={openSections[section.id]}
                onOpenChange={() => toggleSection(section.id)} data-id="d4rsrytpu" data-path="src/components/Chapter2Content.tsx">

                  <CollapsibleTrigger asChild data-id="o33xzhnyq" data-path="src/components/Chapter2Content.tsx">
                    <CardHeader className="hover:bg-muted/50 cursor-pointer transition-colors" data-id="rsvx2bttj" data-path="src/components/Chapter2Content.tsx">
                      <div className="flex items-center justify-between" data-id="6z2ggup9j" data-path="src/components/Chapter2Content.tsx">
                        <div className="flex items-center gap-3" data-id="3aaea7u88" data-path="src/components/Chapter2Content.tsx">
                          <div className="flex items-center gap-2" data-id="6e3tk85dm" data-path="src/components/Chapter2Content.tsx">
                            {section.icon}
                            <Badge variant="outline" data-id="nfbaf1k80" data-path="src/components/Chapter2Content.tsx">{section.id}</Badge>
                          </div>
                          <CardTitle className="text-lg text-right" data-id="3l40n226h" data-path="src/components/Chapter2Content.tsx">{section.title}</CardTitle>
                        </div>
                        <div className="flex items-center gap-2" data-id="6l6i7h695" data-path="src/components/Chapter2Content.tsx">
                          {completedSections.includes(section.id) &&
                        <Badge variant="default" data-id="x7tgj6se8" data-path="src/components/Chapter2Content.tsx">مكتمل</Badge>
                        }
                          {openSections[section.id] ?
                        <ChevronDown className="w-4 h-4" data-id="2s90ave2v" data-path="src/components/Chapter2Content.tsx" /> :

                        <ChevronRight className="w-4 h-4" data-id="lempxn1xk" data-path="src/components/Chapter2Content.tsx" />
                        }
                        </div>
                      </div>
                    </CardHeader>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent data-id="ezypnnn09" data-path="src/components/Chapter2Content.tsx">
                    <CardContent className="pt-0" data-id="mu22hkfin" data-path="src/components/Chapter2Content.tsx">
                      {section.subsections ?
                    <div className="space-y-4" data-id="zetrag2h7" data-path="src/components/Chapter2Content.tsx">
                          {section.subsections.map((subsection) =>
                      <Card key={subsection.id} className="border-l-4 border-l-primary" data-id="d60aoa4dc" data-path="src/components/Chapter2Content.tsx">
                              <CardHeader className="pb-2" data-id="6xt0v7k5s" data-path="src/components/Chapter2Content.tsx">
                                <div className="flex items-center gap-2" data-id="x0g8p463p" data-path="src/components/Chapter2Content.tsx">
                                  <Badge variant="secondary" data-id="3nphh0bum" data-path="src/components/Chapter2Content.tsx">{subsection.id}</Badge>
                                  <CardTitle className="text-base text-right" data-id="y12w4vur4" data-path="src/components/Chapter2Content.tsx">
                                    {subsection.title}
                                  </CardTitle>
                                </div>
                              </CardHeader>
                              <CardContent data-id="auay1rcvr" data-path="src/components/Chapter2Content.tsx">
                                <p className="text-muted-foreground text-right leading-relaxed" data-id="mkny48xeg" data-path="src/components/Chapter2Content.tsx">
                                  {subsection.content}
                                </p>
                              </CardContent>
                            </Card>
                      )}
                        </div> :

                    <p className="text-muted-foreground text-right leading-relaxed" data-id="16qau8tux" data-path="src/components/Chapter2Content.tsx">
                          {section.content}
                        </p>
                    }
                      
                      <div className="mt-4 flex justify-start" data-id="79hmy5dn0" data-path="src/components/Chapter2Content.tsx">
                        <Button
                        onClick={() => markAsCompleted(section.id)}
                        disabled={completedSections.includes(section.id)}
                        size="sm" data-id="ejtorfa30" data-path="src/components/Chapter2Content.tsx">

                          {completedSections.includes(section.id) ? 'مكتمل' : 'وضع علامة كمكتمل'}
                        </Button>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            </motion.div>
          )}
        </TabsContent>
        
        <TabsContent value="formulas" className="space-y-4" data-id="fhgn3r86l" data-path="src/components/Chapter2Content.tsx">
          <div className="grid gap-4" data-id="ov3zr5bfv" data-path="src/components/Chapter2Content.tsx">
            {physicsFormulas.map((formula, index) =>
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }} data-id="us34iwqh7" data-path="src/components/Chapter2Content.tsx">

                <Card data-id="qx5fegqvd" data-path="src/components/Chapter2Content.tsx">
                  <CardHeader data-id="wkgwqf4bu" data-path="src/components/Chapter2Content.tsx">
                    <CardTitle className="text-lg text-right flex items-center gap-2" data-id="4rv0wr5bd" data-path="src/components/Chapter2Content.tsx">
                      <Target className="w-5 h-5" data-id="ytwa3fgld" data-path="src/components/Chapter2Content.tsx" />
                      {formula.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent data-id="rkhjret0d" data-path="src/components/Chapter2Content.tsx">
                    <div className="bg-muted p-4 rounded-lg mb-3" data-id="oo2zux86b" data-path="src/components/Chapter2Content.tsx">
                      <code className="text-lg font-mono text-center block" data-id="fs23kgj94" data-path="src/components/Chapter2Content.tsx">{formula.formula}</code>
                    </div>
                    <p className="text-muted-foreground text-right" data-id="58v3gnytf" data-path="src/components/Chapter2Content.tsx">{formula.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      <Card className="mt-8" data-id="sog2ha3v3" data-path="src/components/Chapter2Content.tsx">
        <CardHeader data-id="hopumguhs" data-path="src/components/Chapter2Content.tsx">
          <CardTitle className="text-right" data-id="eb8ypwq43" data-path="src/components/Chapter2Content.tsx">أهداف التعلم</CardTitle>
        </CardHeader>
        <CardContent data-id="lglcmdqov" data-path="src/components/Chapter2Content.tsx">
          <ul className="list-disc list-inside space-y-2 text-right" data-id="9r5amiknj" data-path="src/components/Chapter2Content.tsx">
            <li data-id="nr9cukmwi" data-path="src/components/Chapter2Content.tsx">فهم الطبيعة المزدوجة للأشعة السينية (موجة وجسيم)</li>
            <li data-id="4rbes130k" data-path="src/components/Chapter2Content.tsx">تعلم الكميات والوحدات المستخدمة في القياس الإشعاعي</li>
            <li data-id="6og8ejgrx" data-path="src/components/Chapter2Content.tsx">فهم آليات تفاعل الإشعاع مع المادة</li>
            <li data-id="0ez5au1nh" data-path="src/components/Chapter2Content.tsx">إتقان الأسس الرياضية للمحاكاة</li>
            <li data-id="f9p15mdnn" data-path="src/components/Chapter2Content.tsx">تطبيق المبادئ الفيزيائية في النمذجة</li>
          </ul>
        </CardContent>
      </Card>
    </div>);

};

export default Chapter2Content;