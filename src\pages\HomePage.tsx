import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BookOpen, Play, Download, Users, Clock, Star, Cpu, BarChart, Monitor, Zap } from 'lucide-react';
import { motion } from 'motion/react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from '@/hooks/useTranslation';
import LanguageSwitcher from '@/components/LanguageSwitcher';

const HomePage = () => {
  const { language } = useLanguage();
  const { t } = useTranslation();
  const isRTL = language === 'ar';

  const courseParts = [
  {
    title: isRTL ? 'الجزء الأول: أساسيات التصوير بالأشعة السينية' : 'Part I: X-Ray Imaging Fundamentals',
    chapters: [
    { number: 1, title: isRTL ? 'مقدمة في التصوير بالأشعة السينية' : 'Introduction to X-Ray Imaging' },
    { number: 2, title: isRTL ? 'الفيزياء الأساسية للأشعة السينية' : 'Basic Physics of X-Rays' }],

    icon: <BookOpen className="w-6 h-6" data-id="nnr0y7q9v" data-path="src/pages/HomePage.tsx" />,
    color: 'bg-blue-500'
  },
  {
    title: isRTL ? 'الجزء الثاني: نمذجة مصدر الأشعة السينية' : 'Part II: X-Ray Source Modeling',
    chapters: [
    { number: 3, title: isRTL ? 'أنبوب الأشعة السينية' : 'X-Ray Tube Design' },
    { number: 4, title: isRTL ? 'فيزياء توليد الأشعة السينية' : 'X-Ray Generation Physics' },
    { number: 5, title: isRTL ? 'مولدات الجهد العالي' : 'High Voltage Generators' },
    { number: 6, title: isRTL ? 'ترشيح شعاع الأشعة السينية' : 'X-Ray Beam Filtration' },
    { number: 7, title: isRTL ? 'محاكاة أطياف الأشعة السينية' : 'X-Ray Spectra Simulation' }],

    icon: <Zap className="w-6 h-6" data-id="9enje56l8" data-path="src/pages/HomePage.tsx" />,
    color: 'bg-green-500'
  },
  {
    title: isRTL ? 'الجزء الرابع: نمذجة الكشف بالأشعة السينية' : 'Part IV: X-Ray Detection Modeling',
    chapters: [
    { number: 11, title: isRTL ? 'أجهزة الكشف بالأشعة السينية' : 'X-Ray Detection Systems' }],

    icon: <Monitor className="w-6 h-6" data-id="hofqtenfn" data-path="src/pages/HomePage.tsx" />,
    color: 'bg-purple-500'
  },
  {
    title: isRTL ? 'الجزء الخامس: التطبيقات المتقدمة' : 'Part V: Advanced Applications',
    chapters: [
    { number: 12, title: isRTL ? 'المستقبل والابتكار' : 'Future and Innovation' }],

    icon: <BarChart className="w-6 h-6" data-id="1i74nxr0o" data-path="src/pages/HomePage.tsx" />,
    color: 'bg-orange-500'
  }];


  const features = [
  {
    icon: <Cpu className="w-8 h-8" data-id="yc45yuepg" data-path="src/pages/HomePage.tsx" />,
    title: t('home.features.3d_models.title'),
    description: t('home.features.3d_models.description')
  },
  {
    icon: <BarChart className="w-8 h-8" data-id="5cf4mvfj3" data-path="src/pages/HomePage.tsx" />,
    title: t('home.features.monte_carlo.title'),
    description: t('home.features.monte_carlo.description')
  },
  {
    icon: <Monitor className="w-8 h-8" data-id="wn3jpxmyb" data-path="src/pages/HomePage.tsx" />,
    title: t('home.features.linear_systems.title'),
    description: t('home.features.linear_systems.description')
  },
  {
    icon: <Zap className="w-8 h-8" data-id="w809g6eeo" data-path="src/pages/HomePage.tsx" />,
    title: t('home.features.data_visualization.title'),
    description: t('home.features.data_visualization.description')
  }];


  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 ${isRTL ? 'rtl' : 'ltr'}`} data-id="f2odxd0y9" data-path="src/pages/HomePage.tsx">
      {/* Navigation */}
      <nav className="container mx-auto px-4 py-6 flex justify-between items-center" data-id="tlv1vgla5" data-path="src/pages/HomePage.tsx">
        <div className="flex items-center gap-2" data-id="2je3q2eq6" data-path="src/pages/HomePage.tsx">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center" data-id="kb3s51qjo" data-path="src/pages/HomePage.tsx">
            <BookOpen className="w-6 h-6 text-white" data-id="lfq8vmrxe" data-path="src/pages/HomePage.tsx" />
          </div>
          <span className="text-xl font-bold text-gray-900" data-id="x1rfaaoi3" data-path="src/pages/HomePage.tsx">{t('site.title')}</span>
        </div>
        <div className="flex items-center gap-4" data-id="okswbbjei" data-path="src/pages/HomePage.tsx">
          <Link to="/course" data-id="3czpx6tz7" data-path="src/pages/HomePage.tsx">
            <Button variant="ghost" data-id="wpnkviskv" data-path="src/pages/HomePage.tsx">{t('nav.course')}</Button>
          </Link>
          <LanguageSwitcher data-id="6v9s7fgg3" data-path="src/pages/HomePage.tsx" />
        </div>
      </nav>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20" data-id="jsor0rzp7" data-path="src/pages/HomePage.tsx">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center max-w-4xl mx-auto" data-id="g9eya3jwh" data-path="src/pages/HomePage.tsx">
          
          <div className="flex justify-center gap-2 mb-6" data-id="mwya4nht8" data-path="src/pages/HomePage.tsx">
            <Badge variant="secondary" className="px-3 py-1" data-id="2kl93ebah" data-path="src/pages/HomePage.tsx">
              {t('home.badges.students_professionals')}
            </Badge>
            <Badge variant="secondary" className="px-3 py-1" data-id="o1pefafre" data-path="src/pages/HomePage.tsx">
              {t('home.badges.interactive_content')}
            </Badge>
            <Badge variant="secondary" className="px-3 py-1" data-id="tweht43nm" data-path="src/pages/HomePage.tsx">
              {t('home.badges.scientific_foundation')}
            </Badge>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight" data-id="114n6092x" data-path="src/pages/HomePage.tsx">
            {t('home.hero.title')}
          </h1>
          <p className="text-xl text-gray-600 mb-8 leading-relaxed" data-id="8zm6tnkav" data-path="src/pages/HomePage.tsx">
            {t('home.hero.subtitle')}
          </p>
          <div className="flex gap-4 justify-center flex-wrap" data-id="bjj0vfic2" data-path="src/pages/HomePage.tsx">
            <Link to="/course" data-id="zmrwzd0y3" data-path="src/pages/HomePage.tsx">
              <Button size="lg" className="px-8 py-3" data-id="066rdpuhe" data-path="src/pages/HomePage.tsx">
                <Play className="w-5 h-5 mr-2" data-id="bwbx19zr1" data-path="src/pages/HomePage.tsx" />
                {t('home.hero.start_course')}
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="px-8 py-3" data-id="rscjy89e3" data-path="src/pages/HomePage.tsx">
              <Download className="w-5 h-5 mr-2" data-id="58t8bz603" data-path="src/pages/HomePage.tsx" />
              {t('home.hero.user_guide')}
            </Button>
          </div>
        </motion.div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-20" data-id="yms2vda8e" data-path="src/pages/HomePage.tsx">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-center mb-16" data-id="gpcabs00m" data-path="src/pages/HomePage.tsx">
          <h2 className="text-3xl font-bold text-gray-900 mb-4" data-id="ax456re9f" data-path="src/pages/HomePage.tsx">{t('home.features.title')}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto" data-id="qv52su231" data-path="src/pages/HomePage.tsx">{t('home.features.subtitle')}</p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8" data-id="mu2ipf7hq" data-path="src/pages/HomePage.tsx">
          {features.map((feature, index) =>
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 + index * 0.1 }} data-id="1zqsootbw" data-path="src/pages/HomePage.tsx">
              <Card className="text-center hover:shadow-lg transition-shadow" data-id="qwhkvqrob" data-path="src/pages/HomePage.tsx">
                <CardHeader data-id="90ax8ctz2" data-path="src/pages/HomePage.tsx">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4 text-white" data-id="s7w87fq1u" data-path="src/pages/HomePage.tsx">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-lg" data-id="ecd6rtifg" data-path="src/pages/HomePage.tsx">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent data-id="03t1p7hbk" data-path="src/pages/HomePage.tsx">
                  <p className="text-gray-600" data-id="jspc5u8a4" data-path="src/pages/HomePage.tsx">{feature.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </div>
      </section>

      {/* Course Content Section */}
      <section className="container mx-auto px-4 py-20" data-id="vwi0vrxg3" data-path="src/pages/HomePage.tsx">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16" data-id="l1f1c2rby" data-path="src/pages/HomePage.tsx">
          <h2 className="text-3xl font-bold text-gray-900 mb-4" data-id="de9a5syp4" data-path="src/pages/HomePage.tsx">{t('home.course_content.title')}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto" data-id="lhz70c3cd" data-path="src/pages/HomePage.tsx">{t('home.course_content.subtitle')}</p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8" data-id="jihtbezjo" data-path="src/pages/HomePage.tsx">
          {courseParts.map((part, partIndex) =>
          <motion.div
            key={partIndex}
            initial={{ opacity: 0, x: partIndex % 2 === 0 ? -20 : 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 + partIndex * 0.1 }} data-id="xv7c4e9ps" data-path="src/pages/HomePage.tsx">
              <Card className="h-full hover:shadow-lg transition-shadow" data-id="s5oh9i64x" data-path="src/pages/HomePage.tsx">
                <CardHeader data-id="lw8m4apq5" data-path="src/pages/HomePage.tsx">
                  <div className="flex items-center gap-3 mb-3" data-id="bd25xqu1w" data-path="src/pages/HomePage.tsx">
                    <div className={`w-12 h-12 ${part.color} rounded-lg flex items-center justify-center text-white`} data-id="wdu7dif1w" data-path="src/pages/HomePage.tsx">
                      {part.icon}
                    </div>
                    <CardTitle className="text-lg" data-id="rsczjn6e5" data-path="src/pages/HomePage.tsx">{part.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent data-id="rdtpzh2b5" data-path="src/pages/HomePage.tsx">
                  <div className="space-y-3" data-id="g53kp944h" data-path="src/pages/HomePage.tsx">
                    {part.chapters.map((chapter, chapterIndex) =>
                  <div key={chapterIndex} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg" data-id="bla9xnagk" data-path="src/pages/HomePage.tsx">
                        <Badge variant="outline" data-id="15yz6l8cf" data-path="src/pages/HomePage.tsx">{chapter.number}</Badge>
                        <span className="text-sm font-medium" data-id="srbyw4s1u" data-path="src/pages/HomePage.tsx">{chapter.title}</span>
                      </div>
                  )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </div>
      </section>

      {/* VR Experience Section */}
      <section className="container mx-auto px-4 py-20" data-id="2u7erdijs" data-path="src/pages/HomePage.tsx">
        <div className="bg-gradient-to-br from-purple-600 to-blue-700 rounded-2xl p-12 text-white" data-id="ejx2djtea" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="grid lg:grid-cols-2 gap-12 items-center" data-id="4nvyvcvtn" data-path="src/pages/HomePage.tsx">
            
            <div className={isRTL ? 'text-right' : 'text-left'} data-id="s9byrwhiu" data-path="src/pages/HomePage.tsx">
              <h2 className="text-3xl font-bold mb-6" data-id="7j0bba0xu" data-path="src/pages/HomePage.tsx">{t('home.vr.title')}</h2>
              <p className="text-lg text-purple-100 mb-8" data-id="rzqnadedn" data-path="src/pages/HomePage.tsx">{t('home.vr.subtitle')}</p>
              <div className="grid md:grid-cols-2 gap-4 mb-8" data-id="tnflitkgt" data-path="src/pages/HomePage.tsx">
                <div className="flex items-center gap-3" data-id="m9anzilgn" data-path="src/pages/HomePage.tsx">
                  <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center" data-id="94s803b8c" data-path="src/pages/HomePage.tsx">
                    <Cpu className="w-5 h-5" data-id="odt5er80t" data-path="src/pages/HomePage.tsx" />
                  </div>
                  <span data-id="41jdq4v99" data-path="src/pages/HomePage.tsx">{t('home.vr.feature1')}</span>
                </div>
                <div className="flex items-center gap-3" data-id="4d6ri3d1q" data-path="src/pages/HomePage.tsx">
                  <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center" data-id="pgevouffq" data-path="src/pages/HomePage.tsx">
                    <BarChart className="w-5 h-5" data-id="r6zavyg1s" data-path="src/pages/HomePage.tsx" />
                  </div>
                  <span data-id="iq6gnm013" data-path="src/pages/HomePage.tsx">{t('home.vr.feature2')}</span>
                </div>
                <div className="flex items-center gap-3" data-id="gvtvbujuw" data-path="src/pages/HomePage.tsx">
                  <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center" data-id="1esjfs4kl" data-path="src/pages/HomePage.tsx">
                    <Monitor className="w-5 h-5" data-id="cvi3qx6q3" data-path="src/pages/HomePage.tsx" />
                  </div>
                  <span data-id="1ldbbvt6b" data-path="src/pages/HomePage.tsx">{t('home.vr.feature3')}</span>
                </div>
                <div className="flex items-center gap-3" data-id="jejv293es" data-path="src/pages/HomePage.tsx">
                  <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center" data-id="bf9bbarky" data-path="src/pages/HomePage.tsx">
                    <Zap className="w-5 h-5" data-id="e36hb1xez" data-path="src/pages/HomePage.tsx" />
                  </div>
                  <span data-id="skmidar36" data-path="src/pages/HomePage.tsx">{t('home.vr.feature4')}</span>
                </div>
              </div>
              <Button size="lg" variant="secondary" className="px-8 py-3" data-id="6rfcci1zc" data-path="src/pages/HomePage.tsx">
                {t('home.vr.discover')}
              </Button>
            </div>
            
            <div className="relative" data-id="8xwda6gxo" data-path="src/pages/HomePage.tsx">
              <div className="w-full h-64 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl flex items-center justify-center" data-id="yckixcm73" data-path="src/pages/HomePage.tsx">
                <div className="text-center" data-id="pwxuvk2hs" data-path="src/pages/HomePage.tsx">
                  <Monitor className="w-16 h-16 mx-auto mb-4 text-white/80" data-id="1zqj8k7ic" data-path="src/pages/HomePage.tsx" />
                  <p className="text-white/80" data-id="s8s0yb4pn" data-path="src/pages/HomePage.tsx">{isRTL ? 'معاينة الواقع الافتراضي' : 'VR Experience Preview'}</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="container mx-auto px-4 py-20" data-id="s369v2odb" data-path="src/pages/HomePage.tsx">
        <div className="grid md:grid-cols-3 gap-8 text-center" data-id="qhel5qp1x" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }} data-id="oau3qoctv" data-path="src/pages/HomePage.tsx">
            <div className="text-4xl font-bold text-blue-600 mb-2" data-id="f0o6t74y9" data-path="src/pages/HomePage.tsx">9</div>
            <div className="text-gray-600" data-id="4kbb5uwpg" data-path="src/pages/HomePage.tsx">{isRTL ? 'فصول تفاعلية' : 'Interactive Chapters'}</div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }} data-id="cembg95w5" data-path="src/pages/HomePage.tsx">
            <div className="text-4xl font-bold text-green-600 mb-2" data-id="fy7nr0bd0" data-path="src/pages/HomePage.tsx">530</div>
            <div className="text-gray-600" data-id="ag4yadqrh" data-path="src/pages/HomePage.tsx">{isRTL ? 'دقيقة تعليمية' : 'Minutes of Content'}</div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }} data-id="anmnum2fd" data-path="src/pages/HomePage.tsx">
            <div className="text-4xl font-bold text-purple-600 mb-2" data-id="zifnbl8ji" data-path="src/pages/HomePage.tsx">100%</div>
            <div className="text-gray-600" data-id="5eesotlzy" data-path="src/pages/HomePage.tsx">{isRTL ? 'محتوى تفاعلي' : 'Interactive Content'}</div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-20" data-id="4tgdc5zsm" data-path="src/pages/HomePage.tsx">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center bg-white rounded-2xl p-12 shadow-lg" data-id="f3as1k27d" data-path="src/pages/HomePage.tsx">
          <h2 className="text-3xl font-bold text-gray-900 mb-4" data-id="4fiy1v0qs" data-path="src/pages/HomePage.tsx">{t('home.cta.title')}</h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto" data-id="ri7t666em" data-path="src/pages/HomePage.tsx">{t('home.cta.subtitle')}</p>
          <div className="flex gap-4 justify-center flex-wrap" data-id="u0xkfqjum" data-path="src/pages/HomePage.tsx">
            <Link to="/course" data-id="gne989hnn" data-path="src/pages/HomePage.tsx">
              <Button size="lg" className="px-8 py-3" data-id="b01z3z61f" data-path="src/pages/HomePage.tsx">
                <Play className="w-5 h-5 mr-2" data-id="ry9jvvnc9" data-path="src/pages/HomePage.tsx" />
                {t('home.cta.start_free')}
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="px-8 py-3" data-id="6pyht2vn3" data-path="src/pages/HomePage.tsx">
              <Download className="w-5 h-5 mr-2" data-id="xt342iby2" data-path="src/pages/HomePage.tsx" />
              {t('home.cta.download_curriculum')}
            </Button>
          </div>
        </motion.div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12" data-id="46yptjs2e" data-path="src/pages/HomePage.tsx">
        <div className="container mx-auto px-4 text-center" data-id="9nhpvd7u3" data-path="src/pages/HomePage.tsx">
          <div className="flex items-center justify-center gap-2 mb-4" data-id="3x8n3u5wp" data-path="src/pages/HomePage.tsx">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center" data-id="22588sgo6" data-path="src/pages/HomePage.tsx">
              <BookOpen className="w-5 h-5 text-white" data-id="7om9rp4ds" data-path="src/pages/HomePage.tsx" />
            </div>
            <span className="text-xl font-bold" data-id="07zdwweqy" data-path="src/pages/HomePage.tsx">{t('site.title')}</span>
          </div>
          <p className="text-gray-400 mb-8" data-id="r9lqfiens" data-path="src/pages/HomePage.tsx">{t('site.description')}</p>
          <div className="text-gray-500 text-sm" data-id="vrnlxutix" data-path="src/pages/HomePage.tsx">
            © 2024 {t('site.title')}. {isRTL ? 'جميع الحقوق محفوظة.' : 'All rights reserved.'}
          </div>
        </div>
      </footer>
    </div>);

};

export default HomePage;